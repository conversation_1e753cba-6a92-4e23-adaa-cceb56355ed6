#!/bin/bash

# Chloe Chatbot Systemd Setup Script
# This script sets up the systemd service for the Chloe chatbot

set -e

echo "🚀 Setting up Chloe Chatbot Systemd Service..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ Please run this script as root (use sudo)${NC}"
    exit 1
fi

# Get the current directory (should be the chatbot directory)
CHATBOT_DIR=$(pwd)
echo -e "${BLUE}📁 Chatbot directory: ${CHATBOT_DIR}${NC}"

# Check if we're in the right directory
if [ ! -f "app.py" ]; then
    echo -e "${RED}❌ app.py not found. Please run this script from the chatbot directory.${NC}"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}⚠️  Virtual environment not found. Creating one...${NC}"
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
else
    echo -e "${GREEN}✅ Virtual environment found${NC}"
fi

# Install gunicorn if not already installed
echo -e "${BLUE}📦 Installing Gunicorn...${NC}"
./venv/bin/pip install gunicorn

# Create memory and flask_session directories if they don't exist
mkdir -p memory
mkdir -p flask_session
chown -R root:root memory flask_session

# Choose service type
echo -e "${YELLOW}Choose service type:${NC}"
echo "1) Simple Flask development server (recommended for testing)"
echo "2) Gunicorn production server (recommended for production)"
read -p "Enter choice (1 or 2): " choice

case $choice in
    1)
        SERVICE_FILE="chloe-chatbot.service"
        SERVICE_NAME="chloe-chatbot"
        echo -e "${BLUE}📝 Using simple Flask server${NC}"
        ;;
    2)
        SERVICE_FILE="chloe-chatbot-gunicorn.service"
        SERVICE_NAME="chloe-chatbot"
        echo -e "${BLUE}📝 Using Gunicorn production server${NC}"
        ;;
    *)
        echo -e "${RED}❌ Invalid choice. Exiting.${NC}"
        exit 1
        ;;
esac

# Update the service file with the correct path
sed -i "s|/root/chloe-chatbot|${CHATBOT_DIR}|g" "$SERVICE_FILE"

# Copy service file to systemd directory
echo -e "${BLUE}📋 Installing systemd service file...${NC}"
cp "$SERVICE_FILE" "/etc/systemd/system/${SERVICE_NAME}.service"

# Reload systemd
echo -e "${BLUE}🔄 Reloading systemd daemon...${NC}"
systemctl daemon-reload

# Enable and start the service
echo -e "${BLUE}🚀 Enabling and starting the service...${NC}"
systemctl enable "$SERVICE_NAME"
systemctl start "$SERVICE_NAME"

# Check status
echo -e "${GREEN}✅ Service setup complete!${NC}"
echo ""
echo -e "${BLUE}📊 Service Status:${NC}"
systemctl status "$SERVICE_NAME" --no-pager

echo ""
echo -e "${GREEN}🎉 Chloe Chatbot is now running as a systemd service!${NC}"
echo ""
echo -e "${YELLOW}📋 Useful commands:${NC}"
echo "  Start:   systemctl start $SERVICE_NAME"
echo "  Stop:    systemctl stop $SERVICE_NAME"
echo "  Restart: systemctl restart $SERVICE_NAME"
echo "  Status:  systemctl status $SERVICE_NAME"
echo "  Logs:    journalctl -u $SERVICE_NAME -f"
echo ""
echo -e "${BLUE}🌐 Your chatbot should be accessible at: http://$(hostname -I | awk '{print $1}'):5001${NC}"
