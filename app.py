from flask import Flask, request, render_template, session, redirect, url_for
from flask_session import Session
import os
from dotenv import load_dotenv
from chatbot import chat_with_chloe
from avatar import generate_avatar
from auth import google_login_required, create_google_blueprint

# Allow insecure transport for development (HTTP instead of HTTPS)
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
# Relax OAuth scope validation for development
os.environ['OAUTHLIB_RELAX_TOKEN_SCOPE'] = '1'

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv("FLASK_SECRET_KEY")
app.config['SESSION_TYPE'] = 'filesystem'
Session(app)

# Register Google OAuth blueprint
google_bp = create_google_blueprint()
app.register_blueprint(google_bp, url_prefix="/login")

@app.route("/", methods=["GET", "POST"])
@google_login_required
def index():
    reply = None
    video_url = None
    if request.method == "POST":
        user_input = request.form["text"]
        reply = chat_with_chloe(user_input)
        video_url = generate_avatar(reply)
    return render_template("index.html", reply=reply, video_url=video_url)

@app.route("/logout")
def logout():
    session.clear()
    print("🚪 User logged out - session cleared")
    return redirect("https://accounts.google.com/logout")

@app.route("/clear-session")
def clear_session():
    """Clear session for debugging"""
    session.clear()
    return "Session cleared. <a href='/'>Go to home</a> | <a href='/debug'>Debug</a>"

@app.route("/debug")
def debug():
    """Debug route to check OAuth configuration"""
    from auth import google
    debug_info = {
        "google_authorized": google.authorized if hasattr(google, 'authorized') else False,
        "google_token": google.token if hasattr(google, 'token') and google.token else None,
        "session_keys": list(session.keys()),
        "session_content": dict(session),
        "google_client_id": os.getenv("GOOGLE_CLIENT_ID")[:10] + "..." if os.getenv("GOOGLE_CLIENT_ID") else None,
        "allowed_users": os.getenv("ALLOWED_USERS", "").split(","),
        "redirect_url": google_bp.redirect_url if hasattr(google_bp, 'redirect_url') else None
    }
    return f"<pre>{debug_info}</pre>"

@app.route("/login")
def login_page():
    """Explicit login page"""
    return render_template("login.html")

# Add a custom callback route to handle OAuth properly
@app.route("/oauth/callback")
def oauth_callback():
    """Custom OAuth callback to handle the redirect properly"""
    print("🔄 Custom OAuth callback called")

    # Get the authorization code from the callback
    code = request.args.get('code')
    state = request.args.get('state')
    error = request.args.get('error')

    print(f"🔄 Code: {code[:20] if code else None}...")
    print(f"🔄 State: {state}")
    print(f"🔄 Error: {error}")

    if error:
        print(f"❌ OAuth error: {error}")
        return f"OAuth error: {error}", 400

    if not code:
        print("❌ No authorization code received")
        return "No authorization code received", 400

    try:
        # Exchange the authorization code for tokens
        import requests

        token_url = "https://oauth2.googleapis.com/token"
        token_data = {
            'client_id': os.getenv('GOOGLE_OAUTH_CLIENT_ID'),
            'client_secret': os.getenv('GOOGLE_OAUTH_CLIENT_SECRET'),
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': 'http://127.0.0.1:5001/oauth/callback'
        }

        print("🔄 Exchanging code for token...")
        token_response = requests.post(token_url, data=token_data)

        if not token_response.ok:
            print(f"❌ Token exchange failed: {token_response.status_code} - {token_response.text}")
            return f"Token exchange failed: {token_response.text}", 400

        token_info = token_response.json()
        access_token = token_info.get('access_token')

        if not access_token:
            print("❌ No access token received")
            return "No access token received", 400

        print("✅ Token exchange successful")

        # Get user info using the access token
        user_info_url = "https://www.googleapis.com/oauth2/v2/userinfo"
        headers = {'Authorization': f'Bearer {access_token}'}

        user_response = requests.get(user_info_url, headers=headers)

        if not user_response.ok:
            print(f"❌ Failed to get user info: {user_response.status_code} - {user_response.text}")
            return f"Failed to get user info: {user_response.text}", 400

        user_info = user_response.json()
        user_email = user_info.get('email')

        print(f"👤 User info retrieved: {user_email}")

        # Check if user is allowed
        allowed_users = os.getenv("ALLOWED_USERS", "").split(",")
        allowed_users = [email.strip() for email in allowed_users if email.strip()]

        if user_email not in allowed_users:
            print(f"🚫 Access denied for {user_email}")
            return f"Access denied for {user_email}. Please contact the administrator.", 403

        # Store user info in session
        session["user_email"] = user_email
        session["user_info"] = user_info
        session["access_token"] = access_token
        session["authenticated"] = True

        print(f"✅ User {user_email} authenticated successfully")

        # Redirect to the main page
        return redirect(url_for("index"))

    except Exception as e:
        print(f"❌ OAuth callback error: {e}")
        return f"OAuth callback error: {e}", 500

@app.route("/test-oauth")
def test_oauth():
    """Test OAuth URL generation"""
    from auth import google_bp
    print(f"🧪 Blueprint name: {google_bp.name}")
    print(f"🧪 Blueprint URL prefix: {google_bp.url_prefix}")
    oauth_url = url_for("google.login")
    print(f"🧪 Generated OAuth URL: {oauth_url}")
    return f"OAuth URL: <a href='{oauth_url}'>{oauth_url}</a><br>Redirect URL: {google_bp.redirect_url}"

if __name__ == "__main__":
    app.run(debug=True, host='127.0.0.1', port=5001)
