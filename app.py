from flask import Flask, request, render_template, session, redirect, url_for
from flask_session import Session
import os
from dotenv import load_dotenv
from chatbot import chat_with_chloe
from avatar import generate_avatar
from auth import google_login_required, create_google_blueprint

# Allow insecure transport for development (HTTP instead of HTTPS)
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv("FLASK_SECRET_KEY")
app.config['SESSION_TYPE'] = 'filesystem'
Session(app)

# Register Google OAuth blueprint
google_bp = create_google_blueprint()
app.register_blueprint(google_bp, url_prefix="/login")

@app.route("/", methods=["GET", "POST"])
@google_login_required
def index():
    reply = None
    video_url = None
    if request.method == "POST":
        user_input = request.form["text"]
        reply = chat_with_chloe(user_input)
        video_url = generate_avatar(reply)
    return render_template("index.html", reply=reply, video_url=video_url)

@app.route("/logout")
def logout():
    session.clear()
    return redirect(url_for("index"))

@app.route("/debug")
def debug():
    """Debug route to check OAuth configuration"""
    from auth import google
    debug_info = {
        "google_authorized": google.authorized if hasattr(google, 'authorized') else False,
        "session_keys": list(session.keys()),
        "google_client_id": os.getenv("GOOGLE_CLIENT_ID")[:10] + "..." if os.getenv("GOOGLE_CLIENT_ID") else None,
        "allowed_users": os.getenv("ALLOWED_USERS", "").split(","),
        "redirect_url": google_bp.redirect_url if hasattr(google_bp, 'redirect_url') else None
    }
    return f"<pre>{debug_info}</pre>"

@app.route("/login")
def login_page():
    """Explicit login page"""
    return render_template("login.html")

# Flask-Dance automatically creates the callback route at /login/callback

# Add OAuth event handlers using Flask-Dance signals
from flask_dance.consumer import oauth_authorized

@oauth_authorized.connect_via(google_bp)
def google_logged_in(blueprint, token):
    """Handle successful OAuth login"""
    print(f"🎉 OAuth login successful via {blueprint.name}!")
    print(f"🎉 Token: {token}")
    if not token:
        print("❌ Failed to log in with Google.")
        return False

    # Get user info
    from auth import google
    resp = google.get("/oauth2/v2/userinfo")
    if not resp.ok:
        print(f"❌ Failed to fetch user info: {resp.text}")
        return False

    user_info = resp.json()
    print(f"👤 User info: {user_info}")

    # Store in session
    session["user_info"] = user_info
    session["user_email"] = user_info.get("email")

    return False  # Don't save token to storage

@app.route("/test-oauth")
def test_oauth():
    """Test OAuth URL generation"""
    from auth import google_bp
    print(f"🧪 Blueprint name: {google_bp.name}")
    print(f"🧪 Blueprint URL prefix: {google_bp.url_prefix}")
    oauth_url = url_for("google.login")
    print(f"🧪 Generated OAuth URL: {oauth_url}")
    return f"OAuth URL: <a href='{oauth_url}'>{oauth_url}</a><br>Redirect URL: {google_bp.redirect_url}"

if __name__ == "__main__":
    app.run(debug=True, host='127.0.0.1', port=5001)
