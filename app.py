from flask import Flask, request, render_template, session, redirect, url_for, send_from_directory
from flask_session import Session
import os
import json
from datetime import datetime
from dotenv import load_dotenv
from chatbot import chat_with_chloe
# from avatar import generate_avatar # generate_avatar is no longer used
from auth import google_login_required, get_oauth_redirect_uri
from werkzeug.utils import secure_filename

# Optional imports for file processing (with graceful fallback)
try:
    import PyPDF2  # type: ignore
    HAS_PDF_SUPPORT = True
except ImportError:
    PyPDF2 = None  # type: ignore
    HAS_PDF_SUPPORT = False

try:
    from docx import Document  # type: ignore
    HAS_DOCX_SUPPORT = True
except ImportError:
    Document = None  # type: ignore
    HAS_DOCX_SUPPORT = False

try:
    from PIL import Image  # type: ignore
    HAS_IMAGE_SUPPORT = True
except ImportError:
    Image = None  # type: ignore
    HAS_IMAGE_SUPPORT = False

# Allow insecure transport for development (HTTP instead of HTTPS)
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
# Relax OAuth scope validation for development
os.environ['OAUTHLIB_RELAX_TOKEN_SCOPE'] = '1'

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv("FLASK_SECRET_KEY")
app.config['SESSION_TYPE'] = 'filesystem'
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024  # 10MB max file size

# Use memory folder for temporary file storage (as per user preference)
MEMORY_FOLDER = 'memory'
os.makedirs(MEMORY_FOLDER, exist_ok=True)

# Set upload folder to memory directory
app.config['UPLOAD_FOLDER'] = MEMORY_FOLDER
TEMP_UPLOAD_DIR = MEMORY_FOLDER

# Allowed file extensions
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'doc', 'txt', 'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def extract_file_content(file_path, file_stream=None):
    """Extract text content from uploaded files"""
    try:
        # Check if file has an extension
        if '.' not in file_path:
            return "File has no extension. Please upload a file with a proper extension."

        file_extension = file_path.rsplit('.', 1)[1].lower()

        if file_extension == 'txt':
            if file_stream:
                # Read directly from file stream if available
                file_stream.seek(0)
                return file_stream.read().decode('utf-8')
            else:
                with open(file_path, 'r', encoding='utf-8') as file:
                    return file.read()

        elif file_extension == 'pdf':
            if not HAS_PDF_SUPPORT:
                return "PDF processing requires PyPDF2. Please install it: pip install PyPDF2"
            try:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    text = ""
                    for page in pdf_reader.pages:
                        text += page.extract_text() + "\n"
                    return text.strip()
            except Exception as e:
                return f"Error reading PDF: {str(e)}"

        elif file_extension in ['docx', 'doc']:
            if not HAS_DOCX_SUPPORT:
                return "DOCX processing requires python-docx. Please install it: pip install python-docx"
            try:
                doc = Document(file_path)
                text = ""
                for paragraph in doc.paragraphs:
                    text += paragraph.text + "\n"
                return text.strip()
            except Exception as e:
                return f"Error reading DOCX: {str(e)}"

        elif file_extension in ['png', 'jpg', 'jpeg', 'gif']:
            if not HAS_IMAGE_SUPPORT:
                return "Image processing requires Pillow. Please install it: pip install Pillow"
            try:
                # For now, just return image info
                with Image.open(file_path) as img:
                    return f"Image uploaded: {img.format} format, {img.size[0]}x{img.size[1]} pixels. Image analysis capabilities coming soon!"
            except Exception as e:
                return f"Error reading image: {str(e)}"

        else:
            return f"Unsupported file type: {file_extension}"

    except Exception as e:
        return f"Error reading file: {str(e)}"

Session(app)

# Custom OAuth routes (no Flask-Dance blueprint needed)

@app.route("/", methods=["GET", "POST"])
@google_login_required
def index():
    # Initialize chat history in session if it doesn't exist
    if 'chat_history' not in session:
        session['chat_history'] = []

    user_input = None
    reply = None

    if request.method == "POST":
        user_input = request.form.get("text", "").strip()
        uploaded_file = request.files.get('file')
        file_content = None
        file_info = None

        # Handle file upload
        if uploaded_file and uploaded_file.filename != '':
            if allowed_file(uploaded_file.filename):
                try:
                    filename = secure_filename(uploaded_file.filename)
                    # Use timestamp to avoid filename conflicts
                    timestamp = str(int(datetime.now().timestamp()))
                    unique_filename = f"{timestamp}_{filename}"
                    file_path = os.path.join(TEMP_UPLOAD_DIR, unique_filename)
                    uploaded_file.save(file_path)

                    # Extract file content based on file type
                    file_content = extract_file_content(file_path)
                    file_info = {
                        'filename': filename,
                        'size': os.path.getsize(file_path)
                    }

                    # Clean up the uploaded file after processing
                    os.remove(file_path)

                except Exception as e:
                    print(f"❌ Error processing file: {e}")
                    file_content = f"Error processing file: {str(e)}"
                    # Clean up file if it exists
                    try:
                        if 'file_path' in locals() and os.path.exists(file_path):
                            os.remove(file_path)
                    except Exception as cleanup_error:
                        print(f"❌ Error cleaning up file: {cleanup_error}")
            else:
                file_content = "Unsupported file type. Please upload PDF, DOCX, DOC, TXT, or image files."

        # Prepare the message for Chloe
        if file_content:
            if user_input:
                full_message = f"User uploaded a file ({file_info['filename'] if file_info else 'unknown'}) with content:\n\n{file_content}\n\nUser's question: {user_input}"
            else:
                full_message = f"User uploaded a file ({file_info['filename'] if file_info else 'unknown'}) with content:\n\n{file_content}\n\nPlease analyze this file and provide insights."
        else:
            full_message = user_input

        if not full_message:
            return render_template("index.html", chat_history=session['chat_history'])

        # history_for_chloe should contain all messages *before* the current user_input
        history_for_chloe = list(session['chat_history']) # Make a copy

        # Add current user message to the session chat_history for display
        display_message = user_input if user_input else f"📎 Uploaded: {file_info['filename']}" if file_info else "File uploaded"
        session['chat_history'].append({"role": "user", "content": display_message})

        # Call Chloe with the history *before* this new user message, and the new user_text
        reply = chat_with_chloe(user_text=full_message, chat_history=history_for_chloe)

        # Add Chloe's reply to history
        if reply:
            session['chat_history'].append({"role": "assistant", "content": reply})

        # Ensure session changes are saved
        session.modified = True

    return render_template("index.html", chat_history=session['chat_history'])

@app.route("/new_chat")
@google_login_required
def new_chat():
    """Start a new chat by saving current history and clearing it"""
    # Save current chat history if it exists
    if 'chat_history' in session and session['chat_history']:
        try:
            # Create memory directory if it doesn't exist
            memory_dir = "memory"
            if not os.path.exists(memory_dir):
                os.makedirs(memory_dir)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            user_email_prefix = session.get("user_email", "unknown_user").split('@')[0]
            filename = os.path.join(memory_dir, f"chat_{user_email_prefix}_{timestamp}.json")

            # Save chat history to JSON file
            with open(filename, 'w') as f:
                json.dump(session['chat_history'], f, indent=4)
            print(f"💾 Chat history saved to {filename}")
        except Exception as e:
            print(f"❌ Error saving chat history: {e}")

    # Clear current chat history
    session['chat_history'] = []
    return redirect(url_for('index'))

@app.route("/logout")
def logout():
    session.clear()
    print("🚪 User logged out - session cleared")
    return redirect("https://accounts.google.com/logout")

@app.route("/clear-session")
def clear_session():
    """Clear session for debugging"""
    session.clear()
    return "Session cleared. <a href='/'>Go to home</a> | <a href='/debug'>Debug</a>"

@app.route("/debug")
def debug():
    """Debug route to check OAuth configuration"""
    debug_info = {
        "session_keys": list(session.keys()),
        "session_content": dict(session),
        "google_client_id": os.getenv("GOOGLE_CLIENT_ID")[:10] + "..." if os.getenv("GOOGLE_CLIENT_ID") else None,
        "allowed_users": os.getenv("ALLOWED_USERS", "").split(","),
        "redirect_url": get_oauth_redirect_uri()
    }
    return f"<pre>{debug_info}</pre>"

@app.route("/login")
def login_page():
    """Explicit login page"""
    return render_template("login.html")

@app.route("/login/google")
def login_google():
    """Start Google OAuth flow"""
    import urllib.parse

    client_id = os.getenv('GOOGLE_CLIENT_ID')
    redirect_uri = get_oauth_redirect_uri()
    scope = 'openid email profile'

    auth_url = (
        f"https://accounts.google.com/o/oauth2/auth?"
        f"client_id={client_id}&"
        f"redirect_uri={urllib.parse.quote(redirect_uri)}&"
        f"scope={urllib.parse.quote(scope)}&"
        f"response_type=code&"
        f"access_type=offline"
    )

    return redirect(auth_url)

@app.route('/static/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('static', filename)

# Add a custom callback route to handle OAuth properly
@app.route("/oauth/callback")
def oauth_callback():
    """Custom OAuth callback to handle the redirect properly"""
    print("🔄 Custom OAuth callback called")

    # Get the authorization code from the callback
    code = request.args.get('code')
    state = request.args.get('state')
    error = request.args.get('error')

    print(f"🔄 Code: {code[:20] if code else None}...")
    print(f"🔄 State: {state}")
    print(f"🔄 Error: {error}")

    if error:
        print(f"❌ OAuth error: {error}")
        return f"OAuth error: {error}", 400

    if not code:
        print("❌ No authorization code received")
        return "No authorization code received", 400

    try:
        # Exchange the authorization code for tokens
        import requests

        token_url = "https://oauth2.googleapis.com/token"
        token_data = {
            'client_id': os.getenv('GOOGLE_CLIENT_ID'),
            'client_secret': os.getenv('GOOGLE_CLIENT_SECRET'),
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': get_oauth_redirect_uri()
        }

        print("🔄 Exchanging code for token...")
        token_response = requests.post(token_url, data=token_data)

        if not token_response.ok:
            print(f"❌ Token exchange failed: {token_response.status_code} - {token_response.text}")
            return f"Token exchange failed: {token_response.text}", 400

        token_info = token_response.json()
        access_token = token_info.get('access_token')

        if not access_token:
            print("❌ No access token received")
            return "No access token received", 400

        print("✅ Token exchange successful")

        # Get user info using the access token
        user_info_url = "https://www.googleapis.com/oauth2/v2/userinfo"
        headers = {'Authorization': f'Bearer {access_token}'}

        user_response = requests.get(user_info_url, headers=headers)

        if not user_response.ok:
            print(f"❌ Failed to get user info: {user_response.status_code} - {user_response.text}")
            return f"Failed to get user info: {user_response.text}", 400

        user_info = user_response.json()
        user_email = user_info.get('email')

        print(f"👤 User info retrieved: {user_email}")

        # Check if user is allowed
        allowed_users = os.getenv("ALLOWED_USERS", "").split(",")
        allowed_users = [email.strip() for email in allowed_users if email.strip()]

        if user_email not in allowed_users:
            print(f"🚫 Access denied for {user_email}")
            return f"Access denied for {user_email}. Please contact the administrator.", 403

        # Store user info in session
        session["user_email"] = user_email
        session["user_info"] = user_info
        session["access_token"] = access_token
        session["authenticated"] = True

        print(f"✅ User {user_email} authenticated successfully")

        # Redirect to the main page
        return redirect(url_for("index"))

    except Exception as e:
        print(f"❌ OAuth callback error: {e}")
        return f"OAuth callback error: {e}", 500

@app.route("/debug-oauth")
def debug_oauth():
    """Debug OAuth configuration"""
    import urllib.parse

    client_id = os.getenv('GOOGLE_CLIENT_ID')
    redirect_uri = get_oauth_redirect_uri()
    scope = 'openid email profile'

    # Show environment variables
    env_redirect = os.getenv('GOOGLE_REDIRECT_URL', 'NOT SET')

    auth_url = (
        f"https://accounts.google.com/o/oauth2/auth?"
        f"client_id={client_id}&"
        f"redirect_uri={urllib.parse.quote(redirect_uri)}&"
        f"scope={urllib.parse.quote(scope)}&"
        f"response_type=code&"
        f"access_type=offline"
    )

    return f"""
    <h2>OAuth Debug Information</h2>
    <p><strong>Environment GOOGLE_REDIRECT_URL:</strong> {env_redirect}</p>
    <p><strong>Function returns:</strong> {redirect_uri}</p>
    <p><strong>Client ID:</strong> {client_id}</p>
    <p><strong>URL-encoded redirect URI:</strong> {urllib.parse.quote(redirect_uri)}</p>
    <p><strong>Scope:</strong> {scope}</p>
    <p><strong>Full OAuth URL:</strong></p>
    <p><a href="{auth_url}" target="_blank">{auth_url}</a></p>
    <hr>
    <p><strong>Instructions:</strong></p>
    <ol>
        <li>Copy this exact redirect URI: <code>{redirect_uri}</code></li>
        <li>Go to <a href="https://console.cloud.google.com/apis/credentials" target="_blank">Google Cloud Console</a></li>
        <li>Find your OAuth 2.0 Client ID: <code>{client_id}</code></li>
        <li>Edit it and add this exact URI to "Authorized redirect URIs"</li>
        <li>Save the changes</li>
        <li>Wait 5-10 minutes for changes to propagate</li>
        <li>Then try the OAuth flow again</li>
    </ol>
    <hr>
    <p><strong>Test OAuth (logout first):</strong> <a href="/logout">Logout</a> then <a href="/">Try OAuth</a></p>
    """

if __name__ == "__main__":
    app.run(host="127.0.0.1", port=5001, debug=True)