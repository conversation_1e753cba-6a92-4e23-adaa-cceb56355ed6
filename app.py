from flask import Flask, request, render_template, session, redirect, url_for
from flask_session import Session
import os
from dotenv import load_dotenv
from chatbot import chat_with_chloe
from avatar import generate_avatar
from auth import google_login_required, create_google_blueprint

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv("FLASK_SECRET_KEY")
app.config['SESSION_TYPE'] = 'filesystem'
Session(app)

# Register Google OAuth blueprint
with app.app_context():
    google_bp = create_google_blueprint()
    app.register_blueprint(google_bp, url_prefix="/login")

@app.route("/", methods=["GET", "POST"])
@google_login_required
def index():
    reply = None
    video_url = None
    if request.method == "POST":
        user_input = request.form["text"]
        reply = chat_with_chloe(user_input)
        video_url = generate_avatar(reply)
    return render_template("index.html", reply=reply, video_url=video_url)

@app.route("/logout")
def logout():
    session.clear()
    return redirect(url_for("index"))

@app.route("/debug")
def debug():
    """Debug route to check OAuth configuration"""
    from auth import google
    debug_info = {
        "google_authorized": google.authorized if hasattr(google, 'authorized') else False,
        "session_keys": list(session.keys()),
        "google_client_id": os.getenv("GOOGLE_CLIENT_ID")[:10] + "..." if os.getenv("GOOGLE_CLIENT_ID") else None,
        "allowed_users": os.getenv("ALLOWED_USERS", "").split(","),
        "redirect_url": google_bp.redirect_url if hasattr(google_bp, 'redirect_url') else None
    }
    return f"<pre>{debug_info}</pre>"

@app.route("/login")
def login_page():
    """Explicit login page"""
    return render_template("login.html")

if __name__ == "__main__":
    app.run(debug=True, host='127.0.0.1', port=5001)
