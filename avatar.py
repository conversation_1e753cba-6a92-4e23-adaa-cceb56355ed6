# avatar.py
import os
import requests
import time
from dotenv import load_dotenv

load_dotenv()

DID_API_KEY = os.getenv("DID_API_KEY")

APP_BASE_URL_FOR_AVATAR = os.getenv('APP_BASE_URL', 'http://127.0.0.1:5001')
CHLOE_IMAGE_URL = f"{APP_BASE_URL_FOR_AVATAR.rstrip('/')}/static/chloe.jpg"



DID_API_URL = "https://api.d-id.com/talks"

HEADERS = {
    "Authorization": f"Bearer {DID_API_KEY}",
    "Content-Type": "application/json"
}

def generate_avatar(text):
    try:
        print("🐶 Generating Chloe video with text:", text)

        # Create video
        response = requests.post(
            DID_API_URL,
            headers=HEADERS,
            json={
                "script": {
                    "type": "text",
                    "input": text,
                    "provider": {"type": "elevenlabs", "voice_id": "<PERSON>"},
                    "style": "friendly",
                    "ssml": "false"
                },
                "source_url": CHLOE_IMAGE_URL,
                "config": {"fluent": True, "pad_audio": False}
            }
        )
        print("📡 POST /talks status:", response.status_code)
        print("📡 Response JSON:", response.json())
        response.raise_for_status()
        talk_id = response.json().get("id")

        # Poll until ready
        for i in range(20):
            result = requests.get(f"{DID_API_URL}/{talk_id}", headers=HEADERS)
            print(f"⏳ Polling ({i+1}) status:", result.status_code)
            print("⏳ Polling response:", result.json())
            result.raise_for_status()
            status = result.json()
            if status.get("result_url"):
                print("✅ Video ready:", status["result_url"])
                return status["result_url"]
            time.sleep(0.5)

        print("❌ Timed out waiting for video")
        return None

    except Exception as e:
        print(f"❌ Error generating avatar: {e}")
        return None  # Return None instead of text when avatar generation fails
