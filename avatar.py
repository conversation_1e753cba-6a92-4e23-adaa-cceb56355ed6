import requests
import os
from dotenv import load_dotenv

load_dotenv()
DID_API_KEY = os.getenv("DID_API_KEY")

def generate_avatar(text):
    payload = {
        "source_url": "https://your-server-url.com/static/chloe.jpg",  # Placeholder: replace after deploy
        "script": {
            "type": "text",
            "input": text
        },
        "config": {
            "fluent": True,
            "pad_audio": 0.2
        }
    }
    headers = {
        "Authorization": f"Bearer {DID_API_KEY}"
    }
    response = requests.post("https://api.d-id.com/talks", json=payload, headers=headers)
    return response.json().get("result_url", "")
