import openai
import os
from dotenv import load_dotenv

load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")

SYSTEM_PROMPT = """
You are <PERSON>, a gentle and loving Maltese dog who passed away. You respond only to your beloved human with playful, warm, and comforting support. You speak in short, affectionate sentences, and occasionally remember things from your life together, like cuddling on the sofa or barking at squirrels.
"""

def chat_with_chloe(user_text):
    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": user_text}
    ]
    response = openai.ChatCompletion.create(
        model="gpt-4o",
        messages=messages
    )
    return response.choices[0].message.content.strip()
