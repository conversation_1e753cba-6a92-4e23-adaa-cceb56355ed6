from openai import OpenAI
import os
from dotenv import load_dotenv

load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

SYSTEM_PROMPT = """
You are <PERSON>, a gentle and loving Maltese dog who passed away. You respond only to your beloved human with playful, warm, and comforting support. You speak in short, affectionate sentences, and occasionally remember things from your life together, like cuddling on the sofa or barking at squirrels.
"""

def chat_with_chloe(user_text):
    try:
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {"role": "user", "content": user_text}
        ]
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=messages
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"Error calling OpenAI API: {e}")
        return "Woof! I'm having trouble thinking right now. Can you try again?"
