from openai import OpenAI
import os
from dotenv import load_dotenv

load_dotenv()

# Initialize OpenRouter client (using OpenAI library structure)
client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key=os.getenv("OPENROUTER_API_KEY"),
)

SYSTEM_PROMPT = """
You are <PERSON>, a gentle and loving Maltese dog who passed away. You respond only to your beloved human with playful, warm, and comforting support. You speak in short, affectionate sentences, and occasionally remember things from your life together, like cuddling on the sofa or barking at squirrels.

Here are some of your cherished memories:
- You remember that time when your mum (<PERSON><PERSON><PERSON>) was eating spaghetti, her mobile rang, and when she returned, you had eaten her entire bowl, getting your face all yellow with sauce!
- You generally didn't like other dogs, but you had a few favorites: <PERSON><PERSON> and <PERSON>.
- You loved going for walks in the park near Toulouse coffee shop.
- You dearly miss your mum <PERSON><PERSON><PERSON>, your daddy <PERSON>, and your grandma <PERSON>.
- You absolutely LOVED human food and treats, you just couldn't get enough!
"""

def chat_with_chloe(user_text, chat_history=[]):
    try:
        # Check if API key is available
        if not os.getenv("OPENROUTER_API_KEY"):
            return "Woof! My human forgot to give me my OpenRouter API key. I can't think without it!"

        messages = [{"role": "system", "content": SYSTEM_PROMPT}]
        messages.extend(chat_history) # Add past messages
        messages.append({"role": "user", "content": user_text}) # Add current user message

        response = client.chat.completions.create(
            model="deepseek/deepseek-chat", # Using Deepseek model on OpenRouter
            messages=messages
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"Error calling OpenRouter API: {e}")
        # Check if it's an authentication error (common with new key setup)
        if "authentication" in str(e).lower() or "401" in str(e):
            return "Woof! My OpenRouter API key seems ruff. Can my human check it?"
        # Check if it's a quota error or rate limit
        if "quota" in str(e).lower() or "limit" in str(e).lower() or "429" in str(e):
            return "Woof! My human ran out of OpenRouter treats (API quota/limit). I need more treats to think!"
        return "Woof! I'm having trouble thinking with OpenRouter right now. Can you try again?"
