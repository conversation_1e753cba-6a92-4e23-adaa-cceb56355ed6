[Unit]
Description=<PERSON> Chatbot Flask Application with Gunicorn
After=network.target
Wants=network-online.target

[Service]
Type=notify
User=root
Group=root
WorkingDirectory=/root/chloe-chatbot
Environment=PATH=/root/chloe-chatbot/venv/bin
Environment=PYTHONPATH=/root/chloe-chatbot
ExecStart=/root/chloe-chatbot/venv/bin/gunicorn --bind 0.0.0.0:5001 --workers 2 --timeout 120 --keep-alive 2 --max-requests 1000 --max-requests-jitter 100 --preload app:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=chloe-chatbot

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ReadWritePaths=/root/chloe-chatbot/memory
ReadWritePaths=/root/chloe-chatbot/flask_session

# Resource limits
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
