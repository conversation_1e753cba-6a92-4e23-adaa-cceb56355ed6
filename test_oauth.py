#!/usr/bin/env python3
"""
Test script to verify Google OAuth configuration
"""
import os
from dotenv import load_dotenv

load_dotenv()

def test_oauth_config():
    """Test OAuth configuration"""
    print("🔍 Testing Google OAuth Configuration")
    print("=" * 50)
    
    # Check required environment variables
    required_vars = [
        "GOOGLE_CLIENT_ID",
        "GOOGLE_CLIENT_SECRET", 
        "FLASK_SECRET_KEY",
        "ALLOWED_USERS"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            # Show partial value for security
            if "SECRET" in var or "KEY" in var:
                display_value = value[:10] + "..." if len(value) > 10 else value
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
    
    if missing_vars:
        print(f"\n❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    # Check redirect URL
    redirect_url = os.getenv("GOOGLE_REDIRECT_URL", "http://127.0.0.1:5001/oauth/callback")
    print(f"🔗 Redirect URL: {redirect_url}")
    
    # Check allowed users
    allowed_users = os.getenv("ALLOWED_USERS", "").split(",")
    allowed_users = [email.strip() for email in allowed_users if email.strip()]
    print(f"👥 Allowed Users ({len(allowed_users)}): {allowed_users}")
    
    print("\n🎯 Configuration Summary:")
    print("1. Make sure your Google Console OAuth app has this redirect URI:")
    print(f"   {redirect_url}")
    print("2. Make sure your OAuth consent screen is configured")
    print("3. Make sure your client ID and secret are correct")
    print("4. Make sure allowed users list includes your email")
    
    return True

if __name__ == "__main__":
    test_oauth_config()
