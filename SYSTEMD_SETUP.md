# 🚀 Systemd Service Setup for <PERSON> Chatbot

This guide will help you set up Chloe Chatbot as a systemd service on your VPS, ensuring it starts automatically on boot and restarts if it crashes.

## 🎯 Benefits of Systemd Service

- ✅ **Auto-start on boot** - Chatbot starts when server reboots
- ✅ **Auto-restart on crash** - Service restarts if it fails
- ✅ **Easy management** - Simple start/stop/restart commands
- ✅ **Logging integration** - Logs available via journalctl
- ✅ **Resource management** - Built-in resource limits
- ✅ **Security** - Runs with proper permissions

## 🔧 Quick Setup (Automated)

### Option 1: Use the Setup Script (Recommended)

```bash
# Make the script executable
chmod +x setup_systemd.sh

# Run the setup script as root
sudo ./setup_systemd.sh
```

The script will:
1. Check your environment
2. Install Gunicorn if needed
3. Create necessary directories
4. Install and configure the systemd service
5. Start the service automatically

## 🔧 Manual Setup

### Option 2: Manual Installation

1. **Copy service file to systemd directory:**

```bash
# For simple Flask server
sudo cp chloe-chatbot.service /etc/systemd/system/

# OR for production Gunicorn server
sudo cp chloe-chatbot-gunicorn.service /etc/systemd/system/chloe-chatbot.service
```

2. **Update paths in service file (if needed):**

```bash
sudo nano /etc/systemd/system/chloe-chatbot.service
```

Update these paths to match your setup:
- `WorkingDirectory=/your/path/to/chloe-chatbot`
- `Environment=PATH=/your/path/to/chloe-chatbot/venv/bin`
- `ExecStart=/your/path/to/chloe-chatbot/venv/bin/python app.py`

3. **Install Gunicorn (for production setup):**

```bash
source venv/bin/activate
pip install gunicorn
```

4. **Reload systemd and start service:**

```bash
sudo systemctl daemon-reload
sudo systemctl enable chloe-chatbot
sudo systemctl start chloe-chatbot
```

## 📋 Service Management Commands

### Basic Commands

```bash
# Start the service
sudo systemctl start chloe-chatbot

# Stop the service
sudo systemctl stop chloe-chatbot

# Restart the service
sudo systemctl restart chloe-chatbot

# Check service status
sudo systemctl status chloe-chatbot

# Enable auto-start on boot
sudo systemctl enable chloe-chatbot

# Disable auto-start on boot
sudo systemctl disable chloe-chatbot
```

### Monitoring and Logs

```bash
# View real-time logs
sudo journalctl -u chloe-chatbot -f

# View recent logs
sudo journalctl -u chloe-chatbot --since "1 hour ago"

# View all logs
sudo journalctl -u chloe-chatbot

# Check if service is running
sudo systemctl is-active chloe-chatbot

# Check if service is enabled
sudo systemctl is-enabled chloe-chatbot
```

## 🔧 Service Configuration Options

### Simple Flask Server (Development)
- **File**: `chloe-chatbot.service`
- **Use case**: Testing, development, low traffic
- **Command**: Direct Python execution

### Gunicorn Server (Production)
- **File**: `chloe-chatbot-gunicorn.service`
- **Use case**: Production, high traffic, better performance
- **Features**: Multiple workers, better resource management

## 🛠️ Troubleshooting

### Service Won't Start

1. **Check service status:**
```bash
sudo systemctl status chloe-chatbot
```

2. **Check logs:**
```bash
sudo journalctl -u chloe-chatbot --since "10 minutes ago"
```

3. **Common issues:**
- Wrong file paths in service file
- Missing virtual environment
- Missing dependencies
- Permission issues

### Fix Permission Issues

```bash
# Fix ownership
sudo chown -R root:root /path/to/chloe-chatbot

# Fix permissions
sudo chmod +x /path/to/chloe-chatbot/app.py
```

### Update Service Configuration

```bash
# Edit service file
sudo nano /etc/systemd/system/chloe-chatbot.service

# Reload after changes
sudo systemctl daemon-reload
sudo systemctl restart chloe-chatbot
```

## 🔒 Security Features

The service includes several security enhancements:
- `NoNewPrivileges=true` - Prevents privilege escalation
- `PrivateTmp=true` - Isolated temporary directory
- `ProtectSystem=strict` - Read-only system directories
- `ReadWritePaths` - Limited write access to specific directories

## 📊 Performance Tuning

### Gunicorn Configuration

The Gunicorn service is configured with:
- **2 workers** - Adjust based on CPU cores
- **120s timeout** - For long-running requests
- **1000 max requests** - Worker recycling
- **Preload app** - Faster startup

### Adjust Workers

Edit the service file to change worker count:
```bash
--workers 4  # Use number of CPU cores
```

## 🎉 Verification

After setup, verify everything is working:

1. **Check service status:**
```bash
sudo systemctl status chloe-chatbot
```

2. **Test the application:**
```bash
curl http://localhost:5001
```

3. **Check if it auto-starts:**
```bash
sudo reboot
# After reboot:
sudo systemctl status chloe-chatbot
```

Your Chloe Chatbot is now running as a professional systemd service! 🎉
