import os
from flask import session, redirect, url_for
from flask_dance.contrib.google import make_google_blueprint, google
from dotenv import load_dotenv
import requests

# Allow insecure transport for development (HTTP instead of HTTPS)
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

load_dotenv()

# Create Google OAuth blueprint
def create_google_blueprint():
    # Use environment variable for redirect URL
    redirect_url = os.getenv("GOOGLE_REDIRECT_URL", "http://127.0.0.1:5001/login/google/authorized")

    print(f"🔧 Creating Google OAuth blueprint with redirect URL: {redirect_url}")
    print(f"🔧 Client ID: {os.getenv('GOOGLE_CLIENT_ID')[:20]}...")
    print(f"🔧 Client Secret: {'*' * 10}")

    blueprint = make_google_blueprint(
        client_id=os.getenv("GOOGLE_CLIENT_ID"),
        client_secret=os.getenv("GOOGLE_CLIENT_SECRET"),
        redirect_url=redirect_url,
        scope=["openid", "email", "profile"],  # Simplified scopes
        storage=None  # Use default session storage
    )

    return blueprint

# Create the blueprint instance
google_bp = create_google_blueprint()

def google_login_required(view_func):
    def wrapper(*args, **kwargs):
        try:
            if not google.authorized:
                print("🔒 Google not authorized — redirecting to login.")
                return redirect(url_for("google.login"))

            # Check if token is valid
            if not hasattr(google, 'token') or not google.token:
                print("🔒 No valid token found — redirecting to login.")
                session.clear()  # Clear any stale session data
                return redirect(url_for("google.login"))

            # Get basic user info via OAuth
            resp = google.get("/oauth2/v2/userinfo")
            if not resp.ok:
                print(f"❌ Failed to fetch user info from Google: {resp.status_code} - {resp.text}")
                # Clear session and redirect to login on auth failure
                session.clear()
                return redirect(url_for("google.login"))

            user_info = resp.json()
            user_email = user_info.get("email")

            if not user_email:
                print("❌ No email found in user info")
                session.clear()
                return redirect(url_for("google.login"))

            print(f"👤 Logged in as: {user_email}")

            # Check if user is allowed
            allowed_users = os.getenv("ALLOWED_USERS", "").split(",")
            allowed_users = [email.strip() for email in allowed_users if email.strip()]  # Clean whitespace
            print(f"✅ Allowed Users: {allowed_users}")

            if user_email not in allowed_users:
                print(f"🚫 Access denied for {user_email}")
                return f"Access denied for {user_email}. Please contact the administrator.", 403

            # Store user email in session
            session["user_email"] = user_email
            session["user_info"] = user_info

            # Optional: Call Google People API for extra info
            people_api_key = os.getenv("GOOGLE_PEOPLE_API_KEY")
            if people_api_key:
                try:
                    access_token = google.token["access_token"]
                    headers = {"Authorization": f"Bearer {access_token}"}
                    params = {"personFields": "names,emailAddresses"}
                    people_api_url = f"https://people.googleapis.com/v1/people/me?key={people_api_key}"
                    people_resp = requests.get(people_api_url, headers=headers, params=params)
                    if people_resp.ok:
                        people_data = people_resp.json()
                        print("📇 Google People API data:", people_data)
                        session["people_data"] = people_data
                    else:
                        print(f"⚠️ Failed to fetch data from Google People API: {people_resp.status_code} - {people_resp.text}")
                except Exception as e:
                    print(f"⚠️ Error calling Google People API: {e}")

            return view_func(*args, **kwargs)

        except Exception as e:
            print(f"❌ Error in authentication: {e}")
            session.clear()
            return redirect(url_for("google.login"))

    wrapper.__name__ = view_func.__name__
    return wrapper
