import os
from flask import session, redirect, url_for
from dotenv import load_dotenv
import requests

# Allow insecure transport for development (HTTP instead of HTTPS)
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
# Relax OAuth scope validation for development
os.environ['OAUTHLIB_RELAX_TOKEN_SCOPE'] = '1'

load_dotenv()

def get_oauth_redirect_uri():
    """Get the OAuth redirect URI, using APP_BASE_URL from .env or defaulting to http://127.0.0.1:5001."""
    base_url = os.getenv('APP_BASE_URL', 'http://127.0.0.1:5001')
    # url_for('oauth_callback') generates the path component, e.g., /oauth/callback
    # It needs an active request or app context.
    # However, this function might be called outside an active request context
    # during the initial setup of the OAuth URL in login_google().
    # For simplicity and given its current usage, we'll append the known path.
    # If url_for was critical here, app_context would be needed.
    callback_path = "/oauth/callback"
    return f"{base_url.rstrip('/')}{callback_path}"

def google_login_required(view_func):
    def wrapper(*args, **kwargs):
        try:
            # Check if user is authenticated via session
            if session.get("authenticated") and session.get("user_email"):
                user_email = session.get("user_email")
                print(f"👤 User authenticated via session: {user_email}")
                return view_func(*args, **kwargs)

            # If not authenticated, redirect to Google OAuth
            print("🔒 User not authenticated — redirecting to Google OAuth")

            # Create the OAuth authorization URL
            import urllib.parse

            client_id = os.getenv('GOOGLE_CLIENT_ID')
            redirect_uri = get_oauth_redirect_uri()
            scope = 'openid email profile'

            auth_url = (
                f"https://accounts.google.com/o/oauth2/auth?"
                f"client_id={client_id}&"
                f"redirect_uri={urllib.parse.quote(redirect_uri)}&"
                f"scope={urllib.parse.quote(scope)}&"
                f"response_type=code&"
                f"access_type=offline"
            )

            return redirect(auth_url)

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return f"Authentication error: {e}", 500

    wrapper.__name__ = view_func.__name__
    return wrapper
