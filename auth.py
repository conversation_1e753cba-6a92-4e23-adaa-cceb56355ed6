import os
from flask import session, redirect, url_for
from flask_dance.contrib.google import make_google_blueprint, google
from dotenv import load_dotenv
import requests

load_dotenv()

google_bp = make_google_blueprint(
    client_id=os.getenv("GOOGLE_CLIENT_ID"),
    client_secret=os.getenv("GOOGLE_CLIENT_SECRET"),
    redirect_url="http://127.0.0.1:5000/login/callback",
    scope=["profile", "email", "https://www.googleapis.com/auth/userinfo.profile", "https://www.googleapis.com/auth/userinfo.email"]
)

def google_login_required(view_func):
    def wrapper(*args, **kwargs):
        if not google.authorized:
            print("🔒 Google not authorized — redirecting to login.")
            return redirect(url_for("google.login"))

        # Get basic user info via OAuth
        resp = google.get("/oauth2/v2/userinfo")
        if not resp.ok:
            print("❌ Failed to fetch user info from Google:", resp.text)
            return "Failed to fetch user info", 403

        user_info = resp.json()
        user_email = user_info.get("email")
        print(f"👤 Logged in as: {user_email}")

        allowed = os.getenv("ALLOWED_USERS", "").split(",")
        print(f"✅ Allowed Users: {allowed}")
        if user_email not in allowed:
            print(f"🚫 Access denied for {user_email}")
            return f"Access denied for {user_email}", 403

        # Store user email in session
        session["user_email"] = user_email

        # Now call Google People API for extra info using API key
        people_api_key = os.getenv("GOOGLE_PEOPLE_API_KEY")
        if people_api_key:
            access_token = google.token["access_token"]
            headers = {"Authorization": f"Bearer {access_token}"}
            params = {"personFields": "names,emailAddresses"}
            people_api_url = f"https://people.googleapis.com/v1/people/me?key={people_api_key}"
            people_resp = requests.get(people_api_url, headers=headers, params=params)
            if people_resp.ok:
                people_data = people_resp.json()
                print("📇 Google People API data:", people_data)
                # You can save this data in session or use as needed
                session["people_data"] = people_data
            else:
                print("⚠️ Failed to fetch data from Google People API:", people_resp.text)

        return view_func(*args, **kwargs)

    wrapper.__name__ = view_func.__name__
    return wrapper
