[Unit]
Description=<PERSON> Chatbot Flask Application
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/chloe-chatbot
Environment=PATH=/root/chloe-chatbot/venv/bin
Environment=PYTHONPATH=/root/chloe-chatbot
ExecStart=/root/chloe-chatbot/venv/bin/python app.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=chloe-chatbot

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/root/chloe-chatbot/memory
ReadWritePaths=/root/chloe-chatbot/flask_session

# Resource limits
LimitNOFILE=65536
TimeoutStartSec=30
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
