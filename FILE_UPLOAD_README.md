# 📎 File Upload Feature

Your Chloe chatbot now supports file uploads! Users can upload documents and images for analysis.

## 🎯 Supported File Types

- **📄 PDF files** (.pdf) - Text extraction
- **📝 Word documents** (.docx, .doc) - Text extraction  
- **📄 Text files** (.txt) - Direct text reading
- **🖼️ Images** (.png, .jpg, .jpeg, .gif) - Basic image info (analysis coming soon)

## 🚀 Installation

### Option 1: Automatic Installation

```bash
python install_file_support.py
```

### Option 2: Manual Installation

```bash
pip install PyPDF2==3.0.1 python-docx==1.1.2 Pillow==10.4.0
```

### Option 3: Install from requirements.txt

```bash
pip install -r requirements.txt
```

## 📱 Mobile-Friendly Features

- **Responsive design** - Works great on phones and tablets
- **Touch-friendly buttons** - Large, easy-to-tap interface
- **Optimized input** - Prevents zoom on iOS devices
- **File preview** - Shows selected file before upload

## 🎨 UI Features

- **📎 Paperclip icon** - Click to attach files
- **➤ Send button** - Modern arrow design
- **File preview** - Shows filename and size
- **Remove file** - Easy file removal with ✕ button
- **Loading states** - Visual feedback during processing

## 🔧 Technical Details

### File Size Limits

- Maximum file size: **10MB**
- Configurable in `app.py`: `MAX_CONTENT_LENGTH`

### File Processing

- Files are temporarily saved to `memory/` folder in the project directory
- Content is extracted and processed immediately
- Files are automatically deleted after processing
- No files are permanently stored on the server
- Unique timestamps prevent filename conflicts
- Memory folder is created automatically if it doesn't exist

### Security Features

- Filename sanitization using `secure_filename()`
- File type validation
- File size limits
- Allowed extensions whitelist

## 🎯 Usage Examples

### Text Analysis

1. Upload a PDF or Word document
2. Ask: "Summarize this document"
3. Chloe will analyze the content and provide insights

### Image Upload

1. Upload an image file
2. Chloe will provide basic image information
3. Advanced image analysis coming soon!

### Combined Input

1. Upload a file
2. Add a specific question in the text box
3. Chloe will analyze the file in context of your question

## 🔄 Chat Features

- **New Chat** button - Start fresh conversations
- **Chat history** - Maintains conversation context
- **File indicators** - Shows when files were uploaded
- **Mobile optimized** - Smooth experience on all devices

## 🛠️ Troubleshooting

### Package Installation Issues

If you get import errors, make sure all packages are installed:

```bash
python -c "import PyPDF2, docx, PIL; print('All packages installed!')"
```

### File Upload Issues

- Check file size (must be < 10MB)
- Verify file type is supported
- Ensure temp directory is accessible and writable

### Mobile Issues

- Clear browser cache if layout looks broken
- Ensure viewport meta tag is present
- Test on different mobile browsers

## 🚀 Future Enhancements

- **Image analysis** with AI vision models
- **Audio file support** (.mp3, .wav)
- **Video file support** (.mp4, .avi)
- **Advanced PDF parsing** (tables, images)
- **Batch file upload**
- **File history/management**
