#!/usr/bin/env python3
"""
Installation script for file upload support packages
Run this to install the required packages for PDF, DOCX, and image processing
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def main():
    """Install all required packages for file upload support"""
    packages = [
        "PyPDF2==3.0.1",      # For PDF processing
        "python-docx==1.1.2", # For DOCX processing  
        "Pillow==10.4.0"      # For image processing
    ]
    
    print("🚀 Installing file upload support packages...")
    print("=" * 50)
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("=" * 50)
    if success_count == len(packages):
        print("🎉 All packages installed successfully!")
        print("\nYour chatbot now supports:")
        print("📄 PDF files (.pdf)")
        print("📝 Word documents (.docx, .doc)")
        print("🖼️  Images (.png, .jpg, .jpeg, .gif)")
        print("📄 Text files (.txt)")
    else:
        print(f"⚠️  {len(packages) - success_count} packages failed to install")
        print("Please check the error messages above and try installing manually:")
        for package in packages:
            print(f"  pip install {package}")

if __name__ == "__main__":
    main()
