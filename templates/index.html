<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat with <PERSON> 🐶</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 10px;
            background-color: #f5f5f5;
            min-height: 100vh;
        }
        .chat-box {
            max-width: 800px;
            margin: auto;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 15px;
            background: #fff;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .avatar {
            width: 150px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        .message {
            margin: 20px 0;
            text-align: left;
        }
        .message strong {
            display: block;
            margin-bottom: 5px;
            color: #444;
        }
        .input-box {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 20px;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 8px;
            border: 1px solid #e0e0e0;
        }
        .input-box input[type="text"] {
            flex: 1;
            padding: 12px 16px;
            font-size: 16px;
            border: none;
            background: transparent;
            outline: none;
        }
        .file-upload-btn {
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: background-color 0.2s;
        }
        .file-upload-btn:hover {
            background: #5a6268;
        }
        .send-button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: background-color 0.2s;
        }
        .send-button:hover:not(.sending) {
            background: #0056b3;
        }
        .send-button.sending {
            background: #6c757d;
            cursor: not-allowed;
        }
        .file-input {
            display: none;
        }
        .file-preview {
            margin: 10px 0;
            padding: 8px;
            background: #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            color: #495057;
        }
        .file-preview .remove-file {
            color: #dc3545;
            cursor: pointer;
            margin-left: 8px;
            font-weight: bold;
        }
        .file-preview.hidden {
            display: none;
        }
        video {
            margin-top: 20px;
            width: 100%;
            max-width: 560px;
            border-radius: 10px;
            border: none;
        }
        .new-chat-container {
            margin-top: 15px;
            text-align: center;
        }
        .plus-icon {
            font-size: 1.2em;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
            .chat-box {
                padding: 10px;
                border-radius: 10px;
                margin: 5px;
            }
            .avatar {
                width: 100px;
            }
            .input-box {
                padding: 6px;
                gap: 6px;
            }
            .input-box input[type="text"] {
                padding: 10px 12px;
                font-size: 16px; /* Prevents zoom on iOS */
            }
            .file-upload-btn, .send-button {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }
            .message {
                margin: 15px 0;
                font-size: 14px;
            }
            h2 {
                font-size: 1.3em;
            }
        }

        @media (max-width: 480px) {
            .chat-box {
                margin: 2px;
                padding: 8px;
            }
            .input-box input[type="text"] {
                font-size: 16px;
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-box">
        <img src="/static/chloe.jpg" alt="Chloe" class="avatar">
        <h2>Hi, I'm Chloe! 🐾</h2>

        <div id="chat-messages">
            {% for message in chat_history %}
                <div class="message">
                    {% if message.role == 'user' %}
                        <strong>You:</strong>
                    {% else %}
                        <strong>Chloe:</strong>
                    {% endif %}
                    {{ message.content }}
                </div>
            {% endfor %}
        </div>

        <form method="POST" enctype="multipart/form-data" class="input-box" id="chatForm">
            <input type="file" id="fileInput" name="file" class="file-input" accept=".pdf,.docx,.doc,.png,.jpg,.jpeg,.gif,.txt">
            <button type="button" class="file-upload-btn" onclick="document.getElementById('fileInput').click()" title="Attach file">
                📎
            </button>
            <input type="text" name="text" id="textInput" placeholder="Ask me anything..." required>
            <button type="submit" class="send-button" id="sendButton" title="Send message">
                ➤
            </button>
        </form>

        <div id="filePreview" class="file-preview hidden">
            <span id="fileName"></span>
            <span class="remove-file" onclick="removeFile()" title="Remove file">✕</span>
        </div>
        <div class="new-chat-container">
            <a href="/new_chat" class="new-chat-button">New Chat <span class="plus-icon">&#43;</span></a>
        </div>
    </div>

    <style>
        .new-chat-button {
            display: inline-block;
            padding: 8px 15px;
            background-color: #6c757d; /* A neutral grey */
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .new-chat-button:hover {
            background-color: #5a6268;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatForm = document.getElementById('chatForm');
            const sendButton = document.getElementById('sendButton');
            const fileInput = document.getElementById('fileInput');
            const filePreview = document.getElementById('filePreview');
            const fileName = document.getElementById('fileName');
            const textInput = document.getElementById('textInput');

            // Handle form submission
            if (chatForm && sendButton) {
                chatForm.addEventListener('submit', function() {
                    sendButton.classList.add('sending');
                    sendButton.disabled = true;
                    sendButton.innerHTML = '⏳';
                    // The button will reset on page reload
                });
            }

            // Handle file selection
            if (fileInput) {
                fileInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // Check file size (max 10MB)
                        if (file.size > 10 * 1024 * 1024) {
                            alert('File size must be less than 10MB');
                            fileInput.value = '';
                            return;
                        }

                        // Show file preview
                        fileName.textContent = `📄 ${file.name} (${formatFileSize(file.size)})`;
                        filePreview.classList.remove('hidden');

                        // Make text input optional when file is selected
                        textInput.required = false;
                        textInput.placeholder = 'Ask about this file... (optional)';
                    }
                });
            }
        });

        function removeFile() {
            const fileInput = document.getElementById('fileInput');
            const filePreview = document.getElementById('filePreview');
            const textInput = document.getElementById('textInput');

            fileInput.value = '';
            filePreview.classList.add('hidden');
            textInput.required = true;
            textInput.placeholder = 'Ask me anything...';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
