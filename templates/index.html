<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat with <PERSON> 🐶</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #fefefe;
        }
        .chat-box {
            max-width: 600px;
            margin: auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #fff;
            text-align: center;
        }
        .avatar {
            width: 150px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        .message {
            margin: 20px 0;
            text-align: left;
        }
        .message strong {
            display: block;
            margin-bottom: 5px;
            color: #444;
        }
        .input-box {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .input-box input[type="text"] {
            flex: 1;
            padding: 10px;
            font-size: 1em;
        }
        .input-box button {
            padding: 10px 20px;
            font-size: 1em;
            background-color: #dc3545; /* Red initial state */
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out; /* Smooth transition */
        }
        .input-box button.sending {
            background-color: #ffb6c1; /* Pink sending state */
            cursor: not-allowed;
        }
        .input-box button:hover:not(.sending) {
            background-color: #c82333; /* Darker red on hover */
        }
        video {
            margin-top: 20px;
            width: 100%;
            max-width: 560px;
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="chat-box">
        <img src="/static/chloe.jpg" alt="Chloe" class="avatar">
        <h2>Hi, I'm Chloe! 🐾</h2>

        <div id="chat-messages">
            {% for message in chat_history %}
                <div class="message">
                    {% if message.role == 'user' %}
                        <strong>You:</strong>
                    {% else %}
                        <strong>Chloe:</strong>
                    {% endif %}
                    {{ message.content }}
                </div>
            {% endfor %}
        </div>

        <form method="POST" class="input-box">
            <input type="text" name="text" placeholder="Ask me anything..." required>
            <button type="submit" id="sendButton">Send</button>
        </form>
        <div style="margin-top: 15px; text-align: center;">
            <a href="/new_chat" class="new-chat-button">New Chat <span style="font-size: 1.2em;">&#43;</span></a>
        </div>
    </div>

    <style>
        .new-chat-button {
            display: inline-block;
            padding: 8px 15px;
            background-color: #6c757d; /* A neutral grey */
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .new-chat-button:hover {
            background-color: #5a6268;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatForm = document.querySelector('.input-box'); // Assuming this is the form
            const sendButton = document.getElementById('sendButton');

            if (chatForm && sendButton) {
                chatForm.addEventListener('submit', function() {
                    sendButton.classList.add('sending');
                    sendButton.disabled = true;
                    // The button will reset on page reload
                });
            }
        });
    </script>
</body>
</html>
